import 'package:flutter/material.dart';

// 封装的进度指示器组件
class OrderProgressIndicator extends StatelessWidget {
  final int currentStep;
  final Color purpleColor;

  const OrderProgressIndicator({
    Key? key,
    required this.currentStep,
    this.purpleColor = const Color(0xFF7B68EE),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final steps = [
      {'icon': Icons.assignment, 'label': 'BOSS已下单'},
      {'icon': Icons.check_circle_outline, 'label': 'Staff已接单'},
      {'icon': Icons.shopping_bag_outlined, 'label': 'Staff已买货品'},
      {'icon': Icons.description_outlined, 'label': '完成订单'},
    ];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(steps.length, (i) {
        int status = _getStepStatus(i);
        return Column(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: status == 1 ? purpleColor : Colors.white,
                border: status != 1 ? Border.all(color: Colors.grey[300]!) : null,
              ),
              child: Icon(
                steps[i]['icon'] as IconData,
                size: 20,
                color: status == 0 ? Colors.grey[400] : 
                       status == 1 ? Colors.white : purpleColor,
              ),
            ),
            SizedBox(height: 8),
            Text(
              steps[i]['label'] as String,
              style: TextStyle(
                fontSize: 10,
                color: status == 0 ? Colors.grey[500] :
                       status == 1 ? Colors.black : purpleColor,
              ),
            ),
          ],
        );
      }),
    );
  }

  int _getStepStatus(int index) {
    if (currentStep == -1) return 0; // 初始状态，全部灰色
    if (currentStep == 4) return 2;  // 全部完成状态
    return index < currentStep ? 2 : index == currentStep ? 1 : 0;
  }
}

// 使用示例页面
class TestPage extends StatefulWidget {
  @override
  _TestPageState createState() => _TestPageState();
}

class _TestPageState extends State<TestPage> {
  int currentStep = -1;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('订单进度指示器')),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 使用封装的组件
          OrderProgressIndicator(currentStep: currentStep),
          SizedBox(height: 40),
          Text('当前状态: ${_getStatusText(currentStep)}'),
          SizedBox(height: 20),
          Wrap(
            spacing: 10,
            children: [
              ElevatedButton(onPressed: () => setState(() => currentStep = -1), child: Text('初始')),
              ElevatedButton(onPressed: () => setState(() => currentStep = 0), child: Text('已下单')),
              ElevatedButton(onPressed: () => setState(() => currentStep = 1), child: Text('已接单')),
              ElevatedButton(onPressed: () => setState(() => currentStep = 2), child: Text('已买货品')),
              ElevatedButton(onPressed: () => setState(() => currentStep = 3), child: Text('完成订单')),
              ElevatedButton(onPressed: () => setState(() => currentStep = 4), child: Text('全部完成')),
            ],
          ),
        ],
      ),
    );
  }

  String _getStatusText(int step) {
    switch (step) {
      case -1: return '初始状态';
      case 0: return 'BOSS已下单';
      case 1: return 'Staff已接单';
      case 2: return 'Staff已买货品';
      case 3: return '完成订单';
      case 4: return '全部完成';
      default: return '未知状态';
    }
  }
}