import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'purchase_order_completed.dart';
import '../../components/common/badge.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '订单追踪',
      theme: ThemeData(
        primarySwatch: Colors.purple,
        scaffoldBackgroundColor: const Color.fromARGB(255, 255, 255, 255),
      ),
      home: const OrderTrackingScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

// 订单信息数据模型
class OrderInfo {
  final String orderId;
  final String itemInfo;
  final String merchant;
  final double itemValue;
  final double serviceFee;
  final double deposit;
  final double totalAmount;
  final String deliveryAddress;
  final String deliveryTime;
  final String volume;
  final String weight;

  OrderInfo({
    required this.orderId,
    required this.itemInfo,
    required this.merchant,
    required this.itemValue,
    required this.serviceFee,
    required this.deposit,
    required this.totalAmount,
    required this.deliveryAddress,
    required this.deliveryTime,
    required this.volume,
    required this.weight,
  });
}

class OrderTrackingScreen extends StatefulWidget {
  final OrderInfo? orderInfo;

  const OrderTrackingScreen({Key? key, this.orderInfo}) : super(key: key);

  @override
  State<OrderTrackingScreen> createState() => _OrderTrackingScreenState();
}

class _OrderTrackingScreenState extends State<OrderTrackingScreen> {
  Timer? _timer;
  GoogleMapController? _mapController;

  // 添加展开状态变量
  bool _isItemInfoExpanded = false;

  // Add the missing purple color constant
  static const Color _purpleColor = Color(0xFF9575CD);

  // Demo 坐标（可替换为真实订单/用户位置）
  static const LatLng _shopLatLng = LatLng(22.198745, 113.543873); // 澳门 大三巴附近
  static const LatLng _destLatLng = LatLng(22.202000, 113.551000);
  static const LatLng _myLatLng = LatLng(22.200500, 113.546000);

  Set<Marker> get _markers => {
    Marker(
      markerId: const MarkerId('shop'),
      position: _shopLatLng,
      infoWindow: const InfoWindow(title: '商店'),
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueViolet),
    ),
    Marker(
      markerId: const MarkerId('dest'),
      position: _destLatLng,
      infoWindow: const InfoWindow(title: '目的地'),
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
    ),
  };

  Future<void> _goToMe() async {
    final controller = _mapController;
    if (controller == null) return;
    await controller.animateCamera(
      CameraUpdate.newCameraPosition(
        const CameraPosition(target: _myLatLng, zoom: 15),
      ),
    );
  }

  // 新增的变量和方法
  String get title => '众觅';
  bool get hasBossBadge => true;
  Color get badgeColor => const Color(0xFF9575CD);
  Widget get badge => BadgeWidget(
        color: badgeColor,
        text: '代买',
        size: const Size(55, 25),
      );

  Widget _buildDotIndicator() {
    return Container(
      margin: const EdgeInsets.only(left: 4),
      width: 4,
      height: 4,
      decoration: const BoxDecoration(
        color: Color(0xFFFF9800),
        shape: BoxShape.circle,
      ),
    );
  }

  // 时间轴区域
  Widget _buildTimelineSection({
    required String time1,
    required String time2,
    required String itemValue,
    required String staffDeposit,
    required String serviceFee,
    required String totalFee,
  }) {
    return Row(
      children: [
        // 时间轴
        Column(
          children: [
            _buildTimePoint(time1, true),
            Container(width: 2, height: 40, color: Colors.grey[300]),
            _buildTimePoint(time2, false),
          ],
        ),
        const SizedBox(width: 20),
        // 金额信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 第一行：物品价值和staff保证金标签
              Row(
                children: [
                  const Spacer(),
                  SizedBox(
                    width: 60,
                    child: Text(
                      '物品价值',
                      textAlign: TextAlign.right,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ),
                  const SizedBox(width: 15),
                  SizedBox(
                    width: 70,
                    child: Text(
                      'Staff保证金',
                      textAlign: TextAlign.right,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ),
                ],
              ),
              // 第一行：对应的金额
              Row(
                children: [
                  const Spacer(),
                  SizedBox(
                    width: 60,
                    child: const Text(
                      '100MOP',
                      textAlign: TextAlign.right,
                      style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                    ),
                  ),
                  const SizedBox(width: 15),
                  SizedBox(
                    width: 70,
                    child: const Text(
                      '10MOP',
                      textAlign: TextAlign.right,
                      style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // 第二行：服务费和已支付标签
              Row(
                children: [
                  const Spacer(),
                  SizedBox(
                    width: 60,
                    child: Text(
                      '服务费',
                      textAlign: TextAlign.right,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ),
                  const SizedBox(width: 15),
                  SizedBox(
                    width: 70,
                    child: Text(
                      '已支付',
                      textAlign: TextAlign.right,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ),
                ],
              ),
              // 第二行：对应的金额
              Row(
                children: [
                  const Spacer(),
                  SizedBox(
                    width: 60,
                    child: const Text(
                      '6MOP',
                      textAlign: TextAlign.right,
                      style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                    ),
                  ),
                  const SizedBox(width: 15),
                  SizedBox(
                    width: 70,
                    child: const Text(
                      '116MOP',
                      textAlign: TextAlign.right,
                      style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  int _remainingSeconds = 2 * 3600 + 28 * 60 + 45; // 2小时28分钟45秒
  int _initialSeconds = 0;


  @override
  void initState() {
    super.initState();
    _initialSeconds = _remainingSeconds;
    _startTimer();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingSeconds > 0) {
          _remainingSeconds--;
        } else {
          timer.cancel();
        }
      });
    });
  }

  String get _formattedTime {
    int hours = _remainingSeconds ~/ 3600;
    int minutes = (_remainingSeconds % 3600) ~/ 60;
    int seconds = _remainingSeconds % 60;
    return '${hours.toString().padLeft(2, '0')} 小时 ${minutes.toString().padLeft(2, '0')} 分钟 ${seconds.toString().padLeft(2, '0')} 秒';
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (!didPop) {
          final shouldExit = await _showExitConfirmDialog(context);
          if (shouldExit && context.mounted) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
        backgroundColor: const Color.fromARGB(255, 255, 255, 255),
        body: SafeArea(
          child: Column(
            children: [
              // 顶部导航栏
              _buildAppBar(),
              // 倒计时进度条
              _buildCountdownBar(),
              // 内容区域
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // 地图区域
                      _buildMapSection(),
                      const SizedBox(height: 12),
                      // 配送信息卡片
                      _buildOrderInfoCard(),
                      const SizedBox(height: 12),
                      // 订单信息卡片
                      _buildOrderDetailInfoCard(),
                      const SizedBox(height: 12),
                      _buildProgressIndicator_order_process_page(),
                      const SizedBox(height: 12),
                      _buildBottomActions(),
                      const SizedBox(height: 80), // 为底部按钮留空间
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        // 底部导航栏
        // bottomNavigationBar: _buildBottomNavBar(),
      ),
    );
  }

  // 显示退出确认对话框
  Future<bool> _showExitConfirmDialog(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: const Text('确认退出'),
            content: const Text('订单正在进行中，确定要退出吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('确定'),
              ),
            ],
          ),
        ) ??
        false;
  }

  // 处理完成订单
  Future<void> _handleCompleteOrder() async {
    try {
      // 显示确认对话框
      final confirmed = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('确认完成订单'),
          content: const Text('确认要完成此订单吗？完成后将无法撤销。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('确认完成'),
            ),
          ],
        ),
      );

      if (confirmed == true && mounted) {
        // 显示处理中的提示
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('正在完成订单...'),
              ],
            ),
          ),
        );

        // 模拟处理延迟
        await Future.delayed(const Duration(seconds: 2));

        if (mounted) {
          // 关闭处理中对话框
          Navigator.of(context).pop();

          // 创建订单完成信息
          final orderCompleteInfo = _createOrderCompleteInfo();

          // 跳转到订单完成页面，清除所有订单相关的中间页面
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) =>
                  OrderCompletePage(orderInfo: orderCompleteInfo),
            ),
            (route) => route.isFirst, // 保留根页面
          );
        }
      }
    } catch (e) {
      // 处理异常情况
      if (mounted) {
        // 如果有处理中对话框，先关闭它
        Navigator.of(context).popUntil((route) => route is! DialogRoute);

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('完成订单失败，请重试'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // 创建订单完成信息
  OrderCompleteInfo _createOrderCompleteInfo() {
    final orderInfo = widget.orderInfo;

    return OrderCompleteInfo(
      orderId:
          orderInfo?.orderId ?? 'ORD${DateTime.now().millisecondsSinceEpoch}',
      itemInfo: orderInfo?.itemInfo ?? '小龙虾',
      merchant: orderInfo?.merchant ?? '店铺A',
      itemValue: orderInfo?.itemValue ?? 100.0,
      serviceFee: orderInfo?.serviceFee ?? 6.0,
      deposit: orderInfo?.deposit ?? 10.0,
      totalAmount: orderInfo?.totalAmount ?? 116.0,
      deliveryAddress: orderInfo?.deliveryAddress ?? '某某街道',
      pickupAddress: orderInfo?.merchant ?? '店铺A',
      deliveryTime: orderInfo?.deliveryTime ?? '1小时0分钟',
      volume: orderInfo?.volume ?? '0',
      weight: orderInfo?.weight ?? '1',
      staffName: 'Marvis Ighedosa',
      staffEmail: '<EMAIL>',
      completedTime: DateTime.now(),
      paymentStatus: '已支付',
    );
  }

  Widget _buildAppBar() {
    return Container(
      height: 56,
      color: Colors.white,
      child: Row(
        children: [
          IconButton(
            icon: const Icon(
              Icons.arrow_back_ios,
              size: 20,
              color: Color(0xFF333333),
            ),
            onPressed: () async {
              final shouldExit = await _showExitConfirmDialog(context);
              if (shouldExit && mounted) {
                Navigator.of(context).pop();
              }
            },
          ),
          const Expanded(
            child: Center(
              child: Text(
                '订单进行中',
                style: TextStyle(
                  fontSize: 17,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFF333333),
                ),
              ),
            ),
          ),
          const SizedBox(width: 48), // 平衡左侧返回按钮
        ],
      ),
    );
  }

  Widget _buildCountdownBar() {
    final hours = _remainingSeconds ~/ 3600;
    final minutes = (_remainingSeconds % 3600) ~/ 60;
    final seconds = _remainingSeconds % 60;
    final progress = (_initialSeconds == 0)
        ? 0.0
        : (1 - _remainingSeconds / _initialSeconds).clamp(0.0, 1.0);

    TextSpan numSpan(String s) => TextSpan(
          text: s,
          style: const TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.w600,
            color: Color(0xFF333333),
          ),
        );
    const unitStyle = TextStyle(fontSize: 12, color: Color(0xFFB0B0B0));

    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center, // 水平居中
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center, // 子控件居中排列
            children: [
              const Text(
                '预计送达时间:',
                style: TextStyle(fontSize: 14, color: Color(0xFF666666)),
          ),
          const SizedBox(width: 12),
          RichText(
            text: TextSpan(children: [
              numSpan(hours.toString().padLeft(2, '0')),
              TextSpan(text: ' 小时 ', style: unitStyle),
              numSpan(minutes.toString().padLeft(2, '0')),
              TextSpan(text: ' 分钟 ', style: unitStyle),
              numSpan(seconds.toString().padLeft(2, '0')),
              TextSpan(text: ' 秒', style: unitStyle),
            ]),
          ),
        ],
      ),
      const SizedBox(height: 12),
      // 进度条布局可保持不变
      LayoutBuilder(builder: (context, constraints) {
        final w = constraints.maxWidth;
        return Container(
          height: 10,
          decoration: BoxDecoration(
            color: const Color(0xFFE9EDF6),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Align(
            alignment: Alignment.centerLeft,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
              width: w * progress,
              decoration: BoxDecoration(
                color: _purpleColor,
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
        );
      }),
    ],
  ),
);

  }

  Widget _buildMapSection() {
    return Container(
      height: 200,
      color: Colors.white,
      child: Stack(
        children: [
          // Google 地图
          ClipRRect(
            borderRadius: BorderRadius.zero,
            child: GoogleMap(
              initialCameraPosition: const CameraPosition(
                target: _shopLatLng,
                zoom: 15,
              ),
              markers: _markers,
              onMapCreated: (controller) => _mapController = controller,
              myLocationEnabled: false, // 如需显示真实定位，可改为 true 并接入定位权限
              myLocationButtonEnabled: false,
              zoomControlsEnabled: false,
              mapToolbarEnabled: false,
              compassEnabled: false,
            ),
          ),
          // 找我按钮（Demo：平移到预设“我的位置”）
          Positioned(
            right: 16,
            bottom: 16,
            child: GestureDetector(
              onTap: _goToMe,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFF9575CD),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Text(
                  '找我',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderInfoCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.all(12),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.location_on_outlined,
                      size: 18,
                      color: Colors.grey,
                    ),
                    const SizedBox(width: 5),
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                    if (hasBossBadge) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Text(
                              'BOSS保证金',
                              style: TextStyle(
                                color: Colors.black87,
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            _buildDotIndicator(),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
                badge,
              ],
            ),
          ),
          // 内容区域
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                // 时间轴和金额信息
                _buildTimelineSection(
                  time1: '10:00am',
                  time2: '11:00am',
                  itemValue: '100MOP',
                  staffDeposit: '10MOP',
                  serviceFee: '6MOP',
                  totalFee: '116MOP',
                ),
                const SizedBox(height: 10),
                // 物品信息行（带展开功能）
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _isItemInfoExpanded = !_isItemInfoExpanded;
                    });
                  },
                  child: Row(
                    children: [
                      Text(
                        '物品信息',
                        style: TextStyle(color: Colors.grey[600], fontSize: 13),
                      ),
                      const SizedBox(width: 4),
                      // 展开/收起按钮
                      AnimatedRotation(
                        turns: _isItemInfoExpanded ? 0.5 : 0.0,
                        duration: const Duration(milliseconds: 200),
                        child: Icon(
                          Icons.keyboard_arrow_down,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                      const Spacer(),
                      const Text(
                        '小龙虾',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                // 展开的详细信息区域
                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                  height: _isItemInfoExpanded ? null : 0,
                  child: AnimatedOpacity(
                    duration: const Duration(milliseconds: 200),
                    opacity: _isItemInfoExpanded ? 1.0 : 0.0,
                    child: _isItemInfoExpanded
                        ? Container(
                            margin: const EdgeInsets.only(top: 8),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              border: Border(
                                top: BorderSide(color: Colors.grey[200]!, width: 1),
                                bottom: BorderSide(color: Colors.grey[200]!, width: 1),
                                left: BorderSide.none,
                                right: BorderSide.none,
                              ),
                            ),
                            child: Column(
                              children: [
                                // 体积信息
                                Row(
                                  children: [
                                    Text(
                                      '体积',
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                        fontSize: 12,
                                      ),
                                    ),
                                    const Spacer(),
                                    const Text(
                                      '3 cm³',
                                      style: TextStyle(
                                        color: Colors.black87,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                // 物品重量信息
                                Row(
                                  children: [
                                    Text(
                                      '物品重量',
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                        fontSize: 12,
                                      ),
                                    ),
                                    const Spacer(),
                                    const Text(
                                      '1 KG',
                                      style: TextStyle(
                                        color: Colors.black87,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          )
                        : const SizedBox.shrink(),
                  ),
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Text(
                      '起送地址',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      '某某街道',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 5),
                Row(
                  children: [
                    Text(
                      '送达地址',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      '某某街道',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                // 取件码和按钮
                Row(
                  children: [
                    Text(
                      '取件码:',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Text(
                      '15a6765',
                      style: TextStyle(
                        fontSize: 13,
                        color: Color(0xFF9575CD),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    ElevatedButton(
                      onPressed: () {},
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF9575CD),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 6,
                        ),
                      ),
                      child: const Text(
                        '接受改价',
                        style: TextStyle(fontSize: 13, color: Colors.white),
                      ),
                    ),
                    const SizedBox(width: 8),
                    OutlinedButton(
                      onPressed: () {},
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Color(0xFF9575CD)),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 6,
                        ),
                      ),
                      child: const Text(
                        '订单异常？',
                        style: TextStyle(
                          fontSize: 13,
                          color: Color(0xFF9575CD),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetailInfoCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.all(12),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.receipt_long_outlined,
                  size: 18,
                  color: Colors.grey,
                ),
                const SizedBox(width: 5),
                const Text(
                  '订单详情',
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
          // 内容区域
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                Row(
                  children: [
                    Text(
                      '订单号',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      '1234567890',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Text(
                      '订单创建时间',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      '2023-01-01 10:00:00',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Text(
                      '订单支付时间',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      '2023-01-01 10:01:00',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Text(
                      '送货时间',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      '立即配送',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Text(
                      '送货限时',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      '1小時30分鐘',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Text(
                      '支付渠道',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      '微信',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Text(
                      'Staff',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      'Marvis Ighedosa',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Text(
                      'Staff電話號碼',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      '66379194',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4), // 电话号码和图标之间的间距
                    Icon(
                      Icons.phone_in_talk,
                      size: 20,
                      color: Color(0xFFABCB8A),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimePoint(String time, bool isStart) {
    return Column(
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: isStart ? const Color(0xFF9575CD) : const Color(0xFF2196F3),
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          time,
          style: const TextStyle(fontSize: 11, color: Color(0xFF999999)),
        ),
      ],
    );
  }

  Widget _buildProductItem(
    String name,
    String brand,
    String price,
    int quantity,
  ) {
    return Row(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(Icons.image, color: Color(0xFF999999)),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                name,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF333333),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                brand,
                style: const TextStyle(fontSize: 12, color: Color(0xFF999999)),
              ),
            ],
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              price,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF333333),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'X$quantity',
              style: const TextStyle(fontSize: 12, color: Color(0xFF999999)),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          OutlinedButton(
            onPressed: () {},
            style: OutlinedButton.styleFrom(
              minimumSize: const Size(double.infinity, 44),
              side: const BorderSide(color: Color(0xFF9575CD)),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(22),
              ),
            ),
            child: const Text(
              '拨打Staff电话',
              style: TextStyle(fontSize: 15, color: Color(0xFF9575CD)),
            ),
          ),
          const SizedBox(height: 12),
          ElevatedButton(
            onPressed: _handleCompleteOrder,
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(double.infinity, 44),
              backgroundColor: const Color(0xFF9575CD),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(22),
              ),
            ),
            child: const Text(
              '完成订单',
              style: TextStyle(fontSize: 15, color: Colors.white),
            ),
          ),
          const SizedBox(height: 8),
          TextButton(
            onPressed: () {},
            child: const Text(
              '取消订单',
              style: TextStyle(fontSize: 14, color: Color(0xFF999999)),
            ),
          ),
        ],
      ),
    );
  }

  // Widget _buildBottomNavBar() {
  //   return Container(
  //     height: 56,
  //     decoration: const BoxDecoration(
  //       color: Colors.white,
  //       border: Border(top: BorderSide(color: Color(0xFFE5E5E5), width: 0.5)),
  //     ),
  //     child: Row(
  //       mainAxisAlignment: MainAxisAlignment.spaceAround,
  //       children: [
  //         _buildNavItem(Icons.receipt_long, 'BOSS订单', true),
  //         _buildNavItem(Icons.list_alt, 'Staff订单', false),
  //         _buildNavItem(Icons.store, 'Staff买家场', false),
  //         _buildNavItem(Icons.local_shipping, '完成订单', false),
  //       ],
  //     ),
  //   );
  // }
  Widget _buildProgressIndicator_order_process_page() {
    final steps = [
      {'icon': Icons.assignment, 'label': 'BOSS已下单', 'active': true},
      {
        'icon': Icons.check_circle_outline,
        'label': 'Staff已接单',
        'active': false,
      },
      {
        'icon': Icons.shopping_bag_outlined,
        'label': 'Staff已买货品',
        'active': false,
      },
      {'icon': Icons.description_outlined, 'label': '完成订单', 'active': false},
    ];

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: steps.map((step) {
          final isActive = step['active'] as bool;
          return Column(
            children: [
              Icon(
                step['icon'] as IconData,
                size: 24,
                color: isActive ? _purpleColor : Colors.grey[400],
              ),
              const SizedBox(height: 4),
              Text(
                step['label'] as String,
                style: TextStyle(
                  fontSize: 10,
                  color: isActive ? _purpleColor : Colors.grey[500],
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildNavItem(IconData icon, String label, bool isActive) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          size: 20,
          color: isActive ? const Color(0xFF9575CD) : const Color(0xFF999999),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: isActive ? const Color(0xFF9575CD) : const Color(0xFF999999),
          ),
        ),
      ],
    );
  }
}
