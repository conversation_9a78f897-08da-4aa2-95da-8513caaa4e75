import 'package:flutter/material.dart';

/// 自定义徽章组件，支持不同颜色和文本的圆角矩形徽章
///
/// 特点：
/// - 左上角和右下角为直角
/// - 右上角和左下角为圆角
/// - 支持自定义颜色、文本和尺寸
class BadgeWidget extends StatelessWidget {
  /// 徽章的背景颜色
  final Color color;

  /// 徽章显示的文本
  final String text;

  /// 徽章的尺寸，默认为 100x50
  final Size size;

  /// 文本样式，默认为白色16号字体
  final TextStyle? textStyle;

  const BadgeWidget({
    super.key,
    required this.color,
    required this.text,
    this.size = const Size(100, 50),
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: size,
      painter: _BadgePainter(
        color: color,
        text: text,
        textStyle: textStyle,
      ),
    );
  }
}

/// 徽章绘制器，负责绘制圆角矩形和文本
class _BadgePainter extends CustomPainter {
  final Color color;
  final String text;
  final TextStyle? textStyle;

  const _BadgePainter({
    required this.color,
    required this.text,
    this.textStyle,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 绘制圆角矩形背景
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final rrect = RRect.fromLTRBAndCorners(
      0, 0, size.width, size.height,
      topLeft: const Radius.circular(0),      // 左上角直角
      topRight: const Radius.circular(18),    // 右上角圆角
      bottomLeft: const Radius.circular(20),  // 左下角圆角
      bottomRight: const Radius.circular(0),  // 右下角直角
    );
    canvas.drawRRect(rrect, paint);

    // 绘制文字
    final defaultTextStyle = const TextStyle(
      color: Colors.white,
      fontSize: 16,
      fontWeight: FontWeight.w500,
    );

    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: textStyle ?? defaultTextStyle,
      ),
      textDirection: TextDirection.ltr,
    )..layout();

    // 文字居中显示
    final offset = Offset(
      (size.width - textPainter.width) / 2,
      (size.height - textPainter.height) / 2,
    );
    textPainter.paint(canvas, offset);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is! _BadgePainter) return true;
    return oldDelegate.color != color ||
           oldDelegate.text != text ||
           oldDelegate.textStyle != textStyle;
  }
}