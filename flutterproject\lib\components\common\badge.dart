import 'package:flutter/material.dart';

void main() => runApp(const MyApp());

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        backgroundColor: const Color(0xFFF4F6FA),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 使用示例
              CustomRoundedRect(
                color: const Color(0xFFB8A8E8),
                text: 'Buy',
              ),
              const SizedBox(height: 20),
              CustomRoundedRect(
                color: Colors.blue,
                text: 'Sell',
              ),
              const SizedBox(height: 20),
              CustomRoundedRect(
                color: Colors.green,
                text: 'Hold',
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// 简化的自定义圆角矩形组件
class CustomRoundedRect extends StatelessWidget {
  final Color color;
  final String text;
  final Size size;

  const CustomRoundedRect({
    super.key,
    required this.color,
    required this.text,
    this.size = const Size(100, 50),
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: size,
      painter: _RoundedRectPainter(color: color, text: text),
    );
  }
}

class _RoundedRectPainter extends CustomPainter {
  final Color color;
  final String text;

  const _RoundedRectPainter({required this.color, required this.text});

  @override
  void paint(Canvas canvas, Size size) {
    // 绘制圆角矩形
    final paint = Paint()..color = color;
    final rrect = RRect.fromLTRBAndCorners(
      0, 0, size.width, size.height,
      topLeft: const Radius.circular(0),
      topRight: const Radius.circular(30),
      bottomLeft: const Radius.circular(30),
      bottomRight: const Radius.circular(0),
    );
    canvas.drawRRect(rrect, paint);

    // 绘制文字
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: const TextStyle(color: Colors.white, fontSize: 16),
      ),
      textDirection: TextDirection.ltr,
    )..layout();

    // 文字居中
    final offset = Offset(
      (size.width - textPainter.width) / 2,
      (size.height - textPainter.height) / 2,
    );
    textPainter.paint(canvas, offset);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}