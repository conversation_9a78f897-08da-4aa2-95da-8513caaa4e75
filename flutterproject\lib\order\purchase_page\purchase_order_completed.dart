import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// 订单完成信息数据模型
class OrderCompleteInfo {
  final String orderId;
  final String itemInfo;
  final String merchant;
  final double itemValue;
  final double serviceFee;
  final double deposit;
  final double totalAmount;
  final String deliveryAddress;
  final String pickupAddress;
  final String deliveryTime;
  final String volume;
  final String weight;
  final String staffName;
  final String staffEmail;
  final DateTime completedTime;
  final String paymentStatus;

  OrderCompleteInfo({
    required this.orderId,
    required this.itemInfo,
    required this.merchant,
    required this.itemValue,
    required this.serviceFee,
    required this.deposit,
    required this.totalAmount,
    required this.deliveryAddress,
    required this.pickupAddress,
    required this.deliveryTime,
    required this.volume,
    required this.weight,
    required this.staffName,
    required this.staffEmail,
    required this.completedTime,
    required this.paymentStatus,
  });
}

class OrderCompletePage extends StatefulWidget {
  final OrderCompleteInfo? orderInfo;

  const OrderCompletePage({super.key, this.orderInfo});

  @override
  State<OrderCompletePage> createState() => _OrderCompletePageState();
}

class _OrderCompletePageState extends State<OrderCompletePage> {
  bool _isItemInfoExpanded = false;
  static const Color _purpleColor = Color(0xFF9575CD);

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (!didPop) {
          await _handleBackNavigation(context);
        }
      },
      child: Scaffold(
        backgroundColor: const Color.fromARGB(255, 255, 255, 255),
        appBar: _buildAppBar(context),
        body: SingleChildScrollView(
          child: Column(
            children: [
              _OrderHeader(orderInfo: widget.orderInfo),
              // const SizedBox(height: 10),
              _buildOrderInfoCard(),
              const SizedBox(height: 10),
              _buildOrderDetailInfoCard(),
              const SizedBox(height: 10),
              _BottomNavigation(),
              _StaffSection(orderInfo: widget.orderInfo),
              _RecommendSection(),
            ],
          ),
        ),
      ),
    );
  }

  // 处理返回导航
  Future<void> _handleBackNavigation(BuildContext context) async {
    final shouldGoHome = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('订单已完成'),
        content: const Text('订单已成功完成，您希望返回到哪里？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('订单列表'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('返回首页'),
          ),
        ],
      ),
    );

    if (context.mounted) {
      if (shouldGoHome == true) {
        // 返回首页，清除所有订单相关页面
        Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
      } else {
        // 返回订单列表（这里可以根据实际需求调整）
        Navigator.of(
          context,
        ).pushNamedAndRemoveUntil('/orders', (route) => route.isFirst);
      }
    }
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      systemOverlayStyle: SystemUiOverlayStyle.dark,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios, color: Colors.black87, size: 20),
        onPressed: () => _handleBackNavigation(context),
      ),
      title: const Text(
        '订单完成',
        style: TextStyle(
          color: Colors.black87,
          fontSize: 17,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildOrderInfoCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.location_on_outlined,
                      size: 18,
                      color: Colors.grey,
                    ),
                    const SizedBox(width: 5),
                    const Text(
                      '众觅',
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Text(
                      'BOSS保证金',
                      style: TextStyle(
                        color: Colors.black87,
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    _buildDotIndicator(),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF9575CD),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    '代买',
                    style: TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                _buildTimelineSection(),
                const SizedBox(height: 10),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _isItemInfoExpanded = !_isItemInfoExpanded;
                    });
                  },
                  child: Row(
                    children: [
                      Text(
                        '物品信息',
                        style: TextStyle(color: Colors.grey[600], fontSize: 13),
                      ),
                      const SizedBox(width: 4),
                      AnimatedRotation(
                        turns: _isItemInfoExpanded ? 0.5 : 0.0,
                        duration: const Duration(milliseconds: 200),
                        child: Icon(
                          Icons.keyboard_arrow_down,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                      const Spacer(),
                      const Text(
                        '小龙虾',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 5),
                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                  height: _isItemInfoExpanded ? null : 0,
                  child: AnimatedOpacity(
                    duration: const Duration(milliseconds: 200),
                    opacity: _isItemInfoExpanded ? 1.0 : 0.0,
                    child: _isItemInfoExpanded
                        ? Container(
                            margin: const EdgeInsets.only(top: 8),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              border: Border(
                                top: BorderSide(
                                  color: Colors.grey[200]!,
                                  width: 1,
                                ),
                                bottom: BorderSide(
                                  color: Colors.grey[200]!,
                                  width: 1,
                                ),
                                left: BorderSide.none,
                                right: BorderSide.none,
                              ),
                            ),
                            child: Column(
                              children: [
                                // 体积信息
                                Row(
                                  children: [
                                    Text(
                                      '体积',
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                        fontSize: 12,
                                      ),
                                    ),
                                    const Spacer(),
                                    const Text(
                                      '3 cm³',
                                      style: TextStyle(
                                        color: Colors.black87,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                // 物品重量信息
                                Row(
                                  children: [
                                    Text(
                                      '物品重量',
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                        fontSize: 12,
                                      ),
                                    ),
                                    const Spacer(),
                                    const Text(
                                      '1 KG',
                                      style: TextStyle(
                                        color: Colors.black87,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          )
                        : const SizedBox.shrink(),
                  ),
                ),

                Row(
                  children: [
                    Text(
                      '起送地址',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      '某某街道',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 5),
                Row(
                  children: [
                    Text(
                      '送达地址',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      '某某街道',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetailInfoCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.all(12),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.receipt_long_outlined,
                  size: 18,
                  color: Colors.grey,
                ),
                const SizedBox(width: 5),
                const Text(
                  '订单详情',
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
          // 内容区域
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                Row(
                  children: [
                    Text(
                      '订单号',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      '1234567890',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Text(
                      '订单创建时间',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      '2023-01-01 10:00:00',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Text(
                      '订单支付时间',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      '2023-01-01 10:01:00',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Text(
                      '送货时间',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      '立即配送',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Text(
                      '送货限时',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      '1小時30分鐘',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Text(
                      '支付渠道',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      '微信',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Text(
                      'Staff',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      'Marvis Ighedosa',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Text(
                      'Staff電話號碼',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    const Spacer(),
                    const Text(
                      '66379194',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDotIndicator() {
    return Container(
      margin: const EdgeInsets.only(left: 4),
      width: 4,
      height: 4,
      decoration: const BoxDecoration(
        color: Color(0xFFFF9800),
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildTimelineSection() {
    return Row(
      children: [
        Column(
          children: [
            _buildTimePoint('10:00am', true),
            Container(width: 2, height: 40, color: Colors.grey[300]),
            _buildTimePoint('11:00am', false),
          ],
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 第一行：物品价值和staff保证金标签
              Row(
                children: [
                  const Spacer(),
                  SizedBox(
                    width: 60,
                    child: Text(
                      '物品价值',
                      textAlign: TextAlign.right,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ),
                  const SizedBox(width: 15),
                  SizedBox(
                    width: 70,
                    child: Text(
                      'Staff保证金',
                      textAlign: TextAlign.right,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ),
                ],
              ),
              // 第一行：对应的金额
              Row(
                children: [
                  const Spacer(),
                  SizedBox(
                    width: 60,
                    child: const Text(
                      '100MOP',
                      textAlign: TextAlign.right,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(width: 15),
                  SizedBox(
                    width: 70,
                    child: const Text(
                      '10MOP',
                      textAlign: TextAlign.right,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // 第二行：服务费和已支付标签
              Row(
                children: [
                  const Spacer(),
                  SizedBox(
                    width: 60,
                    child: Text(
                      '服务费',
                      textAlign: TextAlign.right,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ),
                  const SizedBox(width: 15),
                  SizedBox(
                    width: 70,
                    child: Text(
                      '已支付',
                      textAlign: TextAlign.right,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ),
                ],
              ),
              // 第二行：对应的金额
              Row(
                children: [
                  const Spacer(),
                  SizedBox(
                    width: 60,
                    child: const Text(
                      '6MOP',
                      textAlign: TextAlign.right,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(width: 15),
                  SizedBox(
                    width: 70,
                    child: const Text(
                      '116MOP',
                      textAlign: TextAlign.right,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTimePoint(String time, bool isStart) {
    return Column(
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: isStart ? const Color(0xFF9575CD) : const Color(0xFF2196F3),
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          time,
          style: const TextStyle(fontSize: 11, color: Color(0xFF999999)),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Row(
        children: [
          Text(label, style: TextStyle(color: Colors.grey[600], fontSize: 13)),
          const Spacer(),
          Text(
            value,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 13,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

class _OrderHeader extends StatelessWidget {
  final OrderCompleteInfo? orderInfo;

  const _OrderHeader({this.orderInfo});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: const Color(0xFF9C88FF),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(Icons.check, color: Colors.white, size: 24),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '您的订单已经完成!',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: 2),
                Row(
                  children: [
                    Text(
                      '感谢您的使用！',
                      style: TextStyle(fontSize: 12, color: Color(0xFF666666)),
                    ),

                    Icon(Icons.verified, size: 14, color: Color(0xFF4CAF50)),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _OrderDetails extends StatefulWidget {
  final OrderCompleteInfo? orderInfo;

  const _OrderDetails({this.orderInfo});

  @override
  State<_OrderDetails> createState() => _OrderDetailsState();
}

class _OrderDetailsState extends State<_OrderDetails> {
  bool _isItemInfoExpanded = false;

  // Add missing getters
  String get title => '众觅';
  bool get hasBossBadge => true;
  Color get badgeColor => const Color(0xFF9575CD);
  String get badge => '代买';

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 时间轴和金额信息区域
          Row(
            children: [
              // 时间轴
              Column(
                children: [
                  _buildTimePoint(
                    widget.orderInfo?.completedTime.toString().substring(
                          11,
                          16,
                        ) ??
                        '10:00am',
                    true,
                  ),
                  Container(width: 2, height: 40, color: Colors.grey[300]),
                  _buildTimePoint(
                    widget.orderInfo?.completedTime.toString().substring(
                          11,
                          16,
                        ) ??
                        '10:00am',
                    false,
                  ),
                ],
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  children: [
                    // 第一行：对应的金额
                    Row(
                      children: [
                        const Spacer(),
                        SizedBox(
                          width: 60,
                          child: Text(
                            '${widget.orderInfo?.itemValue.toStringAsFixed(0) ?? '100'}MOP',
                            textAlign: TextAlign.right,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(width: 15),
                        SizedBox(
                          width: 70,
                          child: Text(
                            '${widget.orderInfo?.deposit.toStringAsFixed(0) ?? '10'}MOP',
                            textAlign: TextAlign.right,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    // 第二行：对应的金额
                    Row(
                      children: [
                        const Spacer(),
                        SizedBox(
                          width: 60,
                          child: Text(
                            '${widget.orderInfo?.serviceFee.toStringAsFixed(0) ?? '6'}MOP',
                            textAlign: TextAlign.right,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(width: 15),
                        SizedBox(
                          width: 70,
                          child: Text(
                            '${widget.orderInfo?.totalAmount.toStringAsFixed(0) ?? '116'}MOP',
                            textAlign: TextAlign.right,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Divider(color: Color(0xFFE5E5E5), height: 1),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '物品信息',
                style: TextStyle(fontSize: 14, color: Color(0xFF666666)),
              ),
              Text(
                widget.orderInfo?.itemInfo ?? '小龙虾',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTimePoint(String time, bool isStart) {
    return Column(
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: isStart ? const Color(0xFF9575CD) : const Color(0xFF2196F3),
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          time,
          style: const TextStyle(fontSize: 11, color: Color(0xFF999999)),
        ),
      ],
    );
  }

  Widget _buildTimelineSection({
    required String time1,
    required String time2,
    required String itemValue,
    required String staffDeposit,
    required String serviceFee,
    required String totalFee,
  }) {
    return Row(
      children: [
        // 时间轴
        Column(
          children: [
            _buildTimePoint(time1, true),
            Container(width: 2, height: 40, color: Colors.grey[300]),
            _buildTimePoint(time2, false),
          ],
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            children: [
              Row(
                children: [
                  const Text(
                    '物品价值',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                  const Spacer(),
                  Text(
                    itemValue,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Text(
                    '服务费',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                  const Spacer(),
                  Text(
                    serviceFee,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}

Widget _BottomNavigation() {
  final steps = [
    {'icon': Icons.assignment, 'label': 'BOSS已下单', 'active': true},
    {'icon': Icons.check_circle_outline, 'label': 'Staff已接单', 'active': false},
    {
      'icon': Icons.shopping_bag_outlined,
      'label': 'Staff已买货品',
      'active': false,
    },
    {'icon': Icons.description_outlined, 'label': '完成订单', 'active': false},
  ];

  return Container(
    padding: const EdgeInsets.symmetric(vertical: 12),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: steps.map((step) {
        final isActive = step['active'] as bool;
        return Column(
          children: [
            Icon(
              step['icon'] as IconData,
              size: 24,
              color: isActive ? const Color(0xFF9575CD) : Colors.grey[400],
            ),
            const SizedBox(height: 4),
            Text(
              step['label'] as String,
              style: TextStyle(
                fontSize: 10,
                color: isActive ? const Color(0xFF9575CD) : Colors.grey[500],
              ),
            ),
          ],
        );
      }).toList(),
    ),
  );
}

class _StaffSection extends StatelessWidget {
  final OrderCompleteInfo? orderInfo;

  const _StaffSection({this.orderInfo});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        // borderRadius: BorderRadius.circular(12),
        // boxShadow: [
        //   BoxShadow(
        //     color: Colors.black.withOpacity(0.04),
        //     blurRadius: 8,
        //     offset: const Offset(0, 2),
        //   ),
        // ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Staff',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          const Divider(color: Color(0xFFE5E5E5), height: 0.7),
          const SizedBox(height: 10),
          Center(
            child: Column(
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 52,
                      height: 52,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(100),
                        gradient: const LinearGradient(
                          colors: [Color(0xFFFF6B9D), Color(0xFFFF8E8E)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                      child: const Icon(
                        Icons.person,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 14),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          orderInfo?.staffName ?? 'Marvis Ighedosa',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          orderInfo?.staffEmail ?? '<EMAIL>',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF666666),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            const Text(
                              '评分',
                              style: TextStyle(
                                fontSize: 14,
                                color: Color(0xFF666666),
                              ),
                            ),
                            const SizedBox(width: 8),
                            ...List.generate(
                              5,
                              (index) => Container(
                                margin: const EdgeInsets.only(right: 8),
                                width: 14,
                                height: 14,
                                decoration: BoxDecoration(
                                  color: index < 4
                                      ? const Color(0xFFAA8ACB)
                                      : const Color(0xFFE5E5E5),
                                  shape: BoxShape.circle,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                SizedBox(
                  width: 150, 
                  height: 43,
                  child: ElevatedButton(
                    onPressed: () {
                      // 返回到主頁，清除所有之前的路由
                      Navigator.of(context).popUntil((route) => route.isFirst);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color.fromARGB(255, 170, 138, 203),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                      elevation: 0,
                    ),
                    child: const Text(
                      '回主页',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _RecommendSection extends StatelessWidget {
  const _RecommendSection();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 32),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '推荐模块',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            height: 120,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              gradient: const LinearGradient(
                colors: [Color(0xFFE8E4FF), Color(0xFFF0EFFF)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Stack(
              children: [
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: Container(
                    width: 200,
                    height: 120,
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8),
                        bottomRight: Radius.circular(8),
                      ),
                    ),
                    child: CustomPaint(painter: _CityScapePainter()),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _CityScapePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // 绘制简化的城市天际线
    final buildings = [
      {
        'x': 0.0,
        'width': 30.0,
        'height': 60.0,
        'color': const Color(0xFFB8B5FF),
      },
      {
        'x': 35.0,
        'width': 25.0,
        'height': 80.0,
        'color': const Color(0xFFA8A4FF),
      },
      {
        'x': 65.0,
        'width': 35.0,
        'height': 70.0,
        'color': const Color(0xFFD0CDFF),
      },
      {
        'x': 105.0,
        'width': 20.0,
        'height': 90.0,
        'color': const Color(0xFFB8B5FF),
      },
      {
        'x': 130.0,
        'width': 40.0,
        'height': 65.0,
        'color': const Color(0xFFC8C5FF),
      },
      {
        'x': 175.0,
        'width': 25.0,
        'height': 85.0,
        'color': const Color(0xFFA8A4FF),
      },
    ];

    for (final building in buildings) {
      paint.color = building['color'] as Color;
      canvas.drawRect(
        Rect.fromLTWH(
          building['x'] as double,
          size.height - (building['height'] as double),
          building['width'] as double,
          building['height'] as double,
        ),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
